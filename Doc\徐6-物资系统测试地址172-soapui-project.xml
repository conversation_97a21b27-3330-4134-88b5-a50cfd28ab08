<?xml version="1.0" encoding="UTF-8"?>
<con:soapui-project id="a10e4f31-036c-47d2-859a-c63aa6a42586" activeEnvironment="Default" name="徐6 物资系统测试地址172" resourceRoot="" soapui-version="5.9.0" abortOnError="false" runType="SEQUENTIAL" xmlns:con="http://eviware.com/soapui/config"><con:settings/><con:interface xsi:type="con:WsdlInterface" id="1c3068a8-2a9e-4e81-90b4-f48fe2e5e521" wsaVersion="NONE" name="AuthServiceImplServiceSoapBinding" type="wsdl" bindingName="{http://ws.com/}AuthServiceImplServiceSoapBinding" soapVersion="1_1" anonymous="optional" definition="http://***********:9008/webservice/AuthService?wsdl" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart="http://***********:9008/webservice/AuthService?wsdl"><con:part><con:url>http://***********:9008/webservice/AuthService?wsdl</con:url><con:content><![CDATA[<wsdl:definitions name="AuthServiceImplService" targetNamespace="http://ws.com/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="http://ws.com/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:ns1="http://schemas.xmlsoap.org/soap/http">
  <wsdl:types>
    <xs:schema elementFormDefault="unqualified" targetNamespace="http://ws.com/" version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:element name="accessInterface" type="tns:accessInterface"/>
      <xs:element name="accessInterfaceResponse" type="tns:accessInterfaceResponse"/>
      <xs:element name="getCertificate" type="tns:getCertificate"/>
      <xs:element name="getCertificateResponse" type="tns:getCertificateResponse"/>
      <xs:element name="regist" type="tns:regist"/>
      <xs:element name="registResponse" type="tns:registResponse"/>
      <xs:complexType name="getCertificate">
        <xs:sequence>
          <xs:element minOccurs="0" name="paramsJSONStr" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="getCertificateResponse">
        <xs:sequence>
          <xs:element minOccurs="0" name="String" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="accessInterface">
        <xs:sequence>
          <xs:element minOccurs="0" name="paramsJSONStr" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="accessInterfaceResponse">
        <xs:sequence>
          <xs:element minOccurs="0" name="String" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="regist">
        <xs:sequence>
          <xs:element minOccurs="0" name="paramsJSONStr" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="registResponse">
        <xs:sequence>
          <xs:element minOccurs="0" name="String" type="xs:string"/>
        </xs:sequence>
      </xs:complexType>
    </xs:schema>
  </wsdl:types>
  <wsdl:message name="getCertificate">
    <wsdl:part element="tns:getCertificate" name="parameters"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="accessInterfaceResponse">
    <wsdl:part element="tns:accessInterfaceResponse" name="parameters"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="getCertificateResponse">
    <wsdl:part element="tns:getCertificateResponse" name="parameters"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="accessInterface">
    <wsdl:part element="tns:accessInterface" name="parameters"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="regist">
    <wsdl:part element="tns:regist" name="parameters"></wsdl:part>
  </wsdl:message>
  <wsdl:message name="registResponse">
    <wsdl:part element="tns:registResponse" name="parameters"></wsdl:part>
  </wsdl:message>
  <wsdl:portType name="AuthService">
    <wsdl:operation name="getCertificate">
      <wsdl:input message="tns:getCertificate" name="getCertificate"></wsdl:input>
      <wsdl:output message="tns:getCertificateResponse" name="getCertificateResponse"></wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="accessInterface">
      <wsdl:input message="tns:accessInterface" name="accessInterface"></wsdl:input>
      <wsdl:output message="tns:accessInterfaceResponse" name="accessInterfaceResponse"></wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="regist">
      <wsdl:input message="tns:regist" name="regist"></wsdl:input>
      <wsdl:output message="tns:registResponse" name="registResponse"></wsdl:output>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="AuthServiceImplServiceSoapBinding" type="tns:AuthService">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="getCertificate">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="getCertificate">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getCertificateResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="accessInterface">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="accessInterface">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="accessInterfaceResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="regist">
      <soap:operation soapAction="" style="document"/>
      <wsdl:input name="regist">
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="registResponse">
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="AuthServiceImplService">
    <wsdl:port binding="tns:AuthServiceImplServiceSoapBinding" name="AuthServiceImplPort">
      <soap:address location="http://***********:9008/webservice/AuthService"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>]]></con:content><con:type>http://schemas.xmlsoap.org/wsdl/</con:type></con:part></con:definitionCache><con:endpoints><con:endpoint>http://***********:9008/webservice/AuthService</con:endpoint></con:endpoints><con:operation id="a2405f59-3510-4a38-8287-96e9ec508992" isOneWay="false" action="" name="accessInterface" bindingOperationName="accessInterface" type="Request-Response" outputName="accessInterfaceResponse" inputName="accessInterface" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="b138668e-e7e0-45d7-ac0a-4b2bb64407c4" name="Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://***********:9008/webservice/AuthService</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ws="http://ws.com/">\r
   <soapenv:Header>\r
   <ser:certificate>FrLwEvl1VuSHKi0gtgVb1zgBE5iiRUc684DQfrgCYyHW+6aRGCuEB2L7UJRjGmNOHsdD4TQ7/s5MKFus4qjM8oYajBgXkOgAskx3bgUNBw+mEKTK6B3dWyb1nBr4N7yJh8ti7JiAbl3Syju3TjT9WZXcMBPtHThs3bpH+WbBwmg=</ser:certificate>
   </soapenv:Header>
   <soapenv:Body>\r
      <ws:accessInterface>\r
         <!--Optional:-->\r
         <paramsJSONStr>?</paramsJSONStr>\r
      </ws:accessInterface>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://ws.com/AuthService/accessInterface"/><con:wsrmConfig version="1.2"/></con:call></con:operation><con:operation id="f28f2158-67f4-473a-b090-96900d1cd54f" isOneWay="false" action="" name="getCertificate" bindingOperationName="getCertificate" type="Request-Response" outputName="getCertificateResponse" inputName="getCertificate" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="9c41c95e-a8bb-4ecc-bc93-8ae0d02fd5ad" name="Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://***********:9008/webservice/AuthService</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ws="http://ws.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <ws:getCertificate>\r
         <!--Optional:-->\r
         <paramsJSONStr>{
  "interfaceIdentify": "InventoryResultCallback
",
  "publicKey": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCyiCRlyAI0TThAuwrADSRLDOo2yRyZK8AA5YA1kurYx8RLiHTgqo5paNcJt28XFTK9ild7aI85y3nP5GUHM1U9Ju3kc6PIz0uvYAJPKho+VY1xSv/CB0oPU8RQBHjI8fx29UriHDyWnQDibsYT3IVOxa13BIXvo/GkRGAyTZ4bIwIDAQAB",
  "systemIdentify": "ZYK",
  "tokenExpired": "3"
}
</paramsJSONStr>\r
      </ws:getCertificate>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://ws.com/AuthService/getCertificate"/><con:wsrmConfig version="1.2"/></con:call></con:operation><con:operation id="70e6c6e2-2ea2-49f7-9f4c-c41d708fb281" isOneWay="false" action="" name="regist" bindingOperationName="regist" type="Request-Response" outputName="registResponse" inputName="regist" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="27ee6448-345f-4bb6-a9f6-9b81e828cc06" name="Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://***********:9008/webservice/AuthService</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ws="http://ws.com/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <ws:regist>\r
         <!--Optional:-->\r
         <paramsJSONStr>{"systemIdentify":"ZYK"}</paramsJSONStr>\r
      </ws:regist>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://ws.com/AuthService/regist"/><con:wsrmConfig version="1.2"/></con:call></con:operation></con:interface><con:properties/><con:wssContainer/><con:oAuth2ProfileContainer/><con:oAuth1ProfileContainer/></con:soapui-project>