using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Xml;

namespace SiaSun.LMS.Implement.Interface.iWMS
{
    /// <summary>
    /// 盘点结果回调上报接口 【iWMS提供，SSWMS调用】
    /// 注册(regist) -> 获取凭证(applyToken) -> 携带凭证调用 accessInterface(InventoryResultCallback)
    /// </summary>
    public class InventoryResultCallback : InterfaceBase
    {

        /// <summary>
        /// 访问接口的Response结构
        /// </summary>
        class OutputParam
        {
            public int code { get; set; }
            public string msg { get; set; }
            public string data { get; set; }
        }

        // 盘点结果-物资明细
        public class StockTakeResultGoodsItem
        {
            public string inventoryId { get; set; }
            public string stockTakeTaskId { get; set; }
            public string stockTakeTaskName { get; set; }
            public string stockTakeTaskCode { get; set; }
            public string stockTakeResultId { get; set; }
            public string goodsId { get; set; }
            public string goodsCode { get; set; }
            public string goodsName { get; set; }
            public string goodsVersion { get; set; }
            public int storageNum { get; set; }
            public int lockNum { get; set; }
            public int stockTakeNum { get; set; }
            public int stockTakeOverNum { get; set; }
            public int stockTakeFloorNum { get; set; }
            public string unitId { get; set; }
            public string unitName { get; set; }
            public string brand { get; set; }
            public string warehouseId { get; set; }
            public string warehouseName { get; set; }
            public string warehouseCode { get; set; }
            public string shelfId { get; set; }
            public string shelfName { get; set; }
            public string shelfCode { get; set; }
            public string stockTakeUserId { get; set; }
            public string stockTakeUserName { get; set; }
            public string stockTakePlanStartDate { get; set; }
            public string stockTakePlanEndDate { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        // 最终上报负载
        class InventoryResultPayload
        {
            public string stockTakeId { get; set; }
            public string stockTakeName { get; set; }
            public string stockTakeCode { get; set; }
            public List<StockTakeResultGoodsItem> stockTakeResultGoodsList { get; set; }
        }

        /// <summary>
        /// 调用外部物资系统盘点结果回调。
        /// </summary>
        /// <param name="stockTakeId">盘点计划id</param>
        /// <param name="stockTakeName">盘点计划名称</param>
        /// <param name="stockTakeCode">盘点计划单编号</param>
        /// <param name="stockTakeResultGoodsList">盘点结果明细</param>
        /// <param name="message">返回消息</param>
        /// <returns>是否调用成功</returns>
        public bool IntefaceMethod(string stockTakeId, string stockTakeName, string stockTakeCode,
            List<StockTakeResultGoodsItem> stockTakeResultGoodsList, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                // 基础校验
                if (string.IsNullOrEmpty(stockTakeId) || string.IsNullOrEmpty(stockTakeCode))
                {
                    result = false;
                    message = "参数错误：stockTakeId/stockTakeCode 不能为空";
                    return result;
                }
                if (stockTakeResultGoodsList == null || stockTakeResultGoodsList.Count == 0)
                {
                    result = false;
                    message = "参数错误：stockTakeResultGoodsList 不能为空";
                    return result;
                }

                // Step 1: 注册，获取secrit
                string registInput = Common.JsonHelper.Serializer(new { systemIdentify = "ZYK" });
                if (!InvokeExternal("regist", registInput, out string registOut))
                {
                    result = false;
                    message = $"调用regist失败：{registOut}";
                    return result;
                }


                var registResp = TryParse<OutputParam>(registOut, out string parseErr1);
                if (registResp == null || registResp.code != 200 || string.IsNullOrEmpty(registResp.data))
                {
                    result = false;
                    message = $"regist返回无效：{(registResp == null ? parseErr1 : registResp.msg)}";
                    return result;
                }

                string secrit = registResp.data;

                // Step 2: 获取凭证 certificate
                var applyTokenInputObj = new
                {
                    interfaceIdentify = "InventoryResultCallback",
                    publicKey = secrit,
                    systemIdentify = "ZYK",
                    tokenExpired = "2"
                };
                string applyTokenInput = Common.JsonHelper.Serializer(applyTokenInputObj);
                if (!InvokeExternal("getCertificate", applyTokenInput, out string applyTokenOut))
                {
                    result = false;
                    message = $"调用getCertificate失败：{applyTokenOut}";
                    return result;
                }

                var applyResp = TryParse<OutputParam>(applyTokenOut, out string parseErr2);
                if (applyResp == null || applyResp.code != 200 || string.IsNullOrEmpty(applyResp.data))
                {
                    result = false;
                    message = $"getCertificate返回无效：{(applyResp == null ? parseErr2 : applyResp.msg)}";
                    return result;
                }

                string certificate = applyResp.data;

                // Step 3: 组织业务负载并调用 accessInterface(InventoryResultCallback)
                var payload = new InventoryResultPayload
                {
                    stockTakeId = stockTakeId,
                    stockTakeName = stockTakeName,
                    stockTakeCode = stockTakeCode,
                    stockTakeResultGoodsList = stockTakeResultGoodsList
                };
                string payloadJson = Common.JsonHelper.Serializer(payload);

                // 按Java参考实现，accessInterface 的 paramsJSONStr 结构
                string paramsJSONStr = Common.JsonHelper.Serializer(new
                {
                    systemIdentify = "ZYK",
                    interfaceIdentify = "InventoryResultCallback",
                    certificate = certificate,
                    json = payloadJson
                });

                // 由于accessInterface需要在SOAP Header中携带certificate，
                // 这里直接构造SOAP并发送
                if (!PostAccessInterfaceWithCertificate("accessInterface", certificate, paramsJSONStr, out string accessRespJson, out string httpErr))
                {
                    result = false;
                    message = $"调用accessInterface失败：{httpErr}";
                    return result;
                }

                var accessResp = TryParse<OutputParam>(accessRespJson, out string parseErr3);
                if (accessResp == null || accessResp.code != 200)
                {
                    result = false;
                    message = $"accessInterface返回无效：{(accessResp == null ? parseErr3 : accessResp.msg)}";
                    return result;
                }

                message = "盘点结果上报成功";
                S_Base.sBase.Log.Info($"InventoryResultCallback成功_计划[{stockTakeCode}]_明细数[{stockTakeResultGoodsList.Count}]_traceId[{Guid.NewGuid()}]_信息[{message}]");
            }
            catch (Exception ex)
            {
                result = false;
                message = $"异常_信息[{ex.Message}]";
                S_Base.sBase.Log.Error($"InventoryResultCallback异常：{ex.Message}", ex);
            }

            return result;
        }

        private T TryParse<T>(string jsonOrWrapped, out string error) where T : class
        {
            error = null;
            try
            {
                // 某些WebService返回SOAP中包含<String>{json}</String>，
                // 若传入已是纯json字符串也可直接解析
                string candidate = jsonOrWrapped;
                // 提取可能包裹的XML中的<String>内容
                if (!string.IsNullOrEmpty(candidate) && candidate.Contains("<"))
                {
                    try
                    {
                        var xml = new XmlDocument();
                        xml.LoadXml(candidate);
                        var node = xml.SelectSingleNode("//String");
                        if (node != null)
                        {
                            candidate = node.InnerText;
                        }
                    }
                    catch { /* ignore xml parse */ }
                }
                return Common.JsonHelper.Deserialize<T>(candidate);
            }
            catch (Exception ex)
            {
                error = ex.Message;
                return null;
            }
        }

        private bool PostAccessInterfaceWithCertificate(string methodName, string certificate, string paramsJSONStr, out string responseJson, out string error)
        {
            responseJson = string.Empty;
            error = null;
            try
            {
                // 使用InterfaceBase中配置的ExternalServiceUrl
                string serviceUrl = GetExternalServiceUrl();
                if (string.IsNullOrEmpty(serviceUrl))
                {
                    error = "未配置ExternalServiceUrl";
                    return false;
                }

                // 构建SOAP Envelope，根据其他接口实现和Java参考代码
                var sb = new StringBuilder();
                sb.Append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:ser=\"http://service.ws.admin.wuxicloud.com/\">");
                sb.Append("<soapenv:Header>");
                //sb.Append("<ser:certificate>").Append(System.Security.SecurityElement.Escape(certificate)).Append("</ser:certificate>");
                sb.Append("</soapenv:Header>");
                sb.Append("<soapenv:Body>");
                sb.Append("<ser:").Append(methodName).Append(">");
                sb.Append("<paramsJSONStr>").Append(System.Security.SecurityElement.Escape(paramsJSONStr)).Append("</paramsJSONStr>");
                sb.Append("</ser:").Append(methodName).Append(">");
                sb.Append("</soapenv:Body>");
                sb.Append("</soapenv:Envelope>");

                var req = (HttpWebRequest)WebRequest.Create(serviceUrl);
                req.Method = "POST";
                req.ContentType = "text/xml; charset=utf-8";
                // 根据其他接口实现，SOAPAction应该包含命名空间和方法名
                req.Headers.Add("SOAPAction", "\"http://service.ws.admin.wuxicloud.com/" + methodName + "\"");

                // 记录SOAP请求内容用于调试
                string soapRequest = sb.ToString();
                S_Base.sBase.Log.Info($"SOAP请求内容: {soapRequest}");

                byte[] data = Encoding.UTF8.GetBytes(soapRequest);
                using (var stream = req.GetRequestStream())
                {
                    stream.Write(data, 0, data.Length);
                }

                using (var resp = (HttpWebResponse)req.GetResponse())
                using (var reader = new System.IO.StreamReader(resp.GetResponseStream(), Encoding.UTF8))
                {
                    string respText = reader.ReadToEnd();
                    S_Base.sBase.Log.Info($"SOAP响应内容: {respText}");

                    // 提取Body中的<String>JSON</String>，与其他接口保持一致
                    var xml = new XmlDocument();
                    xml.LoadXml(respText);
                    var node = xml.SelectSingleNode("//String");
                    if (node != null)
                    {
                        responseJson = node.InnerText;
                    }
                    else
                    {
                        // 兜底：直接返回原文
                        responseJson = respText;
                    }
                }

                return true;
            }
            catch (WebException wex)
            {
                error = wex.Message;
                try
                {
                    using (var resp = (HttpWebResponse)wex.Response)
                    using (var reader = new System.IO.StreamReader(resp.GetResponseStream()))
                    {
                        error += " | " + reader.ReadToEnd();
                    }
                }
                catch { }
                return false;
            }
            catch (Exception ex)
            {
                error = ex.Message;
                return false;
            }
        }

        private string GetExternalServiceUrl()
        {
            // InterfaceBase中externalServiceUrl是private静态，这里从配置读取同名键
            return SiaSun.LMS.Common.StringUtil.GetConfig("ExternalServiceUrl");
        }
    }
}

